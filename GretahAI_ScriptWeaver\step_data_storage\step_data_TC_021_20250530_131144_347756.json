{"test_case_id": "TC_021", "timestamp": "2025-05-30T13:11:44.347756", "data_hash": "ba72bdb355919a6d6ca9a6ef5eaed9e24605280198633f62bd4e741f04099c18", "step_count": 4, "metadata": {"source": "hybrid_editing_sync", "ai_steps_count": 3, "manual_steps_count": 1, "sync_timestamp": "2025-05-30T13:11:44.347756", "test_case_objective": "Verify that the system rejects email addresses with excessive special characters.", "hybrid_editing_enabled": true, "has_manual_steps": true, "save_timestamp": "2025-05-30T13:11:44.347756"}, "step_data": [{"step_type": "ui", "action": "navigate", "locator_strategy": "url", "locator": "https://the-internet.herokuapp.com/login", "test_data_param": "", "expected_result": "Page loads successfully", "assertion_type": "page_title", "condition": "", "timeout": 10, "step_no": "1", "_template_id": "nav_to_url", "_is_manual": true, "_created_at": "2025-05-30T13:11:44.347756", "step_description": "Navigate to the URL: https://the-internet.herokuapp.com/login", "_is_locked": false, "_insertion_point": "start"}, {"step_no": "2", "step_type": "ui", "action": "type", "locator_strategy": "css", "locator": "#email", "test_data_param": "{{special_chars_only}}", "expected_result": "error_message", "assertion_type": "element_visible", "condition": "", "timeout": 10, "step_description": "Enter an email address with only special characters into the email field", "_is_ai_generated": true, "_is_locked": true}, {"step_no": "3", "step_type": "ui", "action": "type", "locator_strategy": "css", "locator": "#email", "test_data_param": "{{special_chars_interspaced}}", "expected_result": "error_message", "assertion_type": "element_visible", "condition": "", "timeout": 10, "step_description": "Enter an email address with special characters interspersed into the email field", "_is_ai_generated": true, "_is_locked": true}, {"step_no": "4", "step_type": "ui", "action": "click", "locator_strategy": "css", "locator": "#next_button", "test_data_param": "", "expected_result": "button_inactive_or_error", "assertion_type": "element_attribute", "condition": "", "timeout": 10, "step_description": "Click the 'Next' button after entering an email with excessive special characters", "_is_ai_generated": true, "_is_locked": true}]}