"""
Simple Stage Test

Test the core stage determination logic to validate the fixes.
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from state_manager import StateManager, StateStage

def test_stage_analysis():
    """Test the _analyze_state_for_stage method directly."""
    print("=== Testing _analyze_state_for_stage ===")
    
    state_manager = StateManager()
    
    # Create a mock state object with Stage 4 progress
    class MockState:
        def __init__(self):
            self.uploaded_excel = "test.xlsx"
            self.test_cases = [{"Test Case ID": "TC001"}]
            self.website_url = "https://test.com"
            self.selected_test_case = {"Test Case ID": "TC001"}
            self.conversion_done = True
            self.step_table_json = [{"Step No": 1}]
    
    mock_state = MockState()
    
    # Test the stage analysis
    determined_stage = state_manager._analyze_state_for_stage(mock_state)
    
    print(f"Determined stage: {determined_stage}")
    print(f"Expected: {StateStage.STAGE4_DETECT}")
    
    if determined_stage == StateStage.STAGE4_DETECT:
        print("✅ Stage analysis: PASS")
        return True
    else:
        print("❌ Stage analysis: FAIL")
        return False

def test_advance_to_safety():
    """Test the advance_to safety mechanisms."""
    print("\n=== Testing advance_to Safety ===")
    
    state = StateManager()
    
    # Set up state with progress
    state.uploaded_excel = "test.xlsx"
    state.test_cases = [{"Test Case ID": "TC001"}]
    state.website_url = "https://test.com"
    state.current_stage = StateStage.STAGE3_CONVERT
    
    # Try to advance to Stage 1 (should be blocked)
    result = state.advance_to(StateStage.STAGE1_UPLOAD, "Test reversion attempt")
    
    if result == False and state.current_stage == StateStage.STAGE3_CONVERT:
        print("✅ advance_to safety: PASS")
        return True
    else:
        print(f"❌ advance_to safety: FAIL - result={result}, stage={state.current_stage}")
        return False

def test_update_stage_safety():
    """Test the update_stage_based_on_completion safety."""
    print("\n=== Testing update_stage_based_on_completion Safety ===")
    
    state = StateManager()
    
    # Set up state with significant progress
    state.uploaded_excel = "test.xlsx"
    state.test_cases = [{"Test Case ID": "TC001"}]
    state.website_url = "https://test.com"
    state.selected_test_case = {"Test Case ID": "TC001"}
    state.conversion_done = True
    state.step_table_json = [{"Step No": 1}]
    state.current_stage = StateStage.STAGE4_DETECT
    
    # Test that update_stage_based_on_completion doesn't revert to Stage 1
    original_stage = state.current_stage
    result = state.update_stage_based_on_completion()
    
    if state.current_stage != StateStage.STAGE1_UPLOAD:
        print("✅ update_stage safety: PASS")
        return True
    else:
        print(f"❌ update_stage safety: FAIL - reverted to Stage 1 from {original_stage}")
        return False

def run_simple_tests():
    """Run the simple stage tests."""
    print("🧪 Running Simple Stage Tests")
    print("=" * 40)
    
    tests = [
        test_stage_analysis,
        test_advance_to_safety,
        test_update_stage_safety
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 40)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL SIMPLE TESTS PASSED!")
        return True
    else:
        print("🚨 Some tests failed!")
        return False

if __name__ == "__main__":
    success = run_simple_tests()
    sys.exit(0 if success else 1)
