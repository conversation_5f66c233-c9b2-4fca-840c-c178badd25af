"""
Comprehensive State Management Testing Suite

This module provides extensive testing of the GretahAI ScriptWeaver state management fixes
to validate they completely resolve the Stage 1 reversion issue.

Test Categories:
1. Integration Testing - Real-world upgrade scenarios
2. Stress Testing - High-load conditions
3. Edge Case Testing - Problematic scenarios
4. End-to-End Workflow Testing - Complete user workflows
5. Regression Testing - Existing functionality preservation
6. Real Application Testing - Full Streamlit environment
"""

import sys
import os
import time
import threading
import logging
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
import traceback

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from state_manager import StateManager, StateStage
from utils.stage_monitor import get_stage_monitor, record_stage_transition

# Configure logging for detailed test output
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("ComprehensiveStateTest")

class TestResults:
    """Track test results across all test categories."""
    def __init__(self):
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.test_details = []
        self.critical_failures = []

    def add_result(self, test_name, passed, details="", is_critical=False):
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
            status = "✅ PASS"
        else:
            self.failed_tests += 1
            status = "❌ FAIL"
            if is_critical:
                self.critical_failures.append(test_name)

        self.test_details.append(f"{status}: {test_name} - {details}")
        print(f"{status}: {test_name}")
        if details:
            print(f"    Details: {details}")

    def print_summary(self):
        print("\n" + "="*80)
        print("🧪 COMPREHENSIVE TEST RESULTS SUMMARY")
        print("="*80)
        print(f"Total Tests: {self.total_tests}")
        print(f"Passed: {self.passed_tests}")
        print(f"Failed: {self.failed_tests}")
        print(f"Success Rate: {(self.passed_tests/self.total_tests)*100:.1f}%")

        if self.critical_failures:
            print(f"\n🚨 CRITICAL FAILURES ({len(self.critical_failures)}):")
            for failure in self.critical_failures:
                print(f"  - {failure}")

        if self.failed_tests == 0:
            print("\n🎉 ALL TESTS PASSED! Stage 1 reversion issue is RESOLVED!")
        else:
            print(f"\n⚠️  {self.failed_tests} tests failed. Review issues above.")

        return self.failed_tests == 0

class MockStreamlit:
    """Enhanced mock Streamlit object for comprehensive testing."""
    def __init__(self):
        self.session_state = {}
        self.rerun_called = False
        self.rerun_count = 0

    def rerun(self):
        self.rerun_called = True
        self.rerun_count += 1

def create_progress_state(stage_level):
    """Create a state object with progress up to the specified stage level."""
    class ProgressState:
        def __init__(self, level):
            # Stage 1 progress (level 0 = no progress, level 1+ = file uploaded)
            self.uploaded_excel = "test.xlsx" if level >= 1 else None
            self.uploaded_file = "test.xlsx" if level >= 1 else None
            self.test_cases = [{"Test Case ID": "TC001"}] if level >= 1 else None

            # Stage 2 progress
            self.website_url = "https://test.com" if level >= 2 else None

            # Stage 3 progress
            self.selected_test_case = {"Test Case ID": "TC001"} if level >= 3 else None
            self.conversion_done = True if level >= 3 else False
            self.step_table_json = [{"Step No": 1}] if level >= 3 else []

            # Stage 4 progress
            self.selected_step = {"Step No": 1} if level >= 4 else None
            self.step_matches = {"element1": "match1"} if level >= 4 else {}
            self.element_matches = {"element1": "match1"} if level >= 4 else {}

            # Stage 5 progress
            self.test_data = {"username": "test"} if level >= 5 else {}
            self.test_data_skipped = False if level >= 5 else False

            # Stage 6 progress
            self.generated_script_path = "test_script.py" if level >= 6 else None

            # Stage 7 progress
            self.all_steps_done = True if level >= 7 else False

            # Add ALL required fields to prevent upgrade errors
            self.script_manually_edited = False
            self.original_ai_script_content = ""
            self.manual_edit_timestamp = None
            self.script_edit_mode = False
            self.script_history = []
            self.script_metadata = {}
            self._script_storage = None
            self.execution_error_occurred = False
            self.execution_error_acknowledged = False
            self.execution_error_details = {}

            # Add fields that might be checked during upgrade
            self.ai_generated_steps = None
            self.manual_steps = []
            self.step_insertion_points = {}
            self.hybrid_editing_enabled = False
            self.combined_step_table = None
            self.converted_test_case_id = None

        def _init_script_storage(self):
            """Mock script storage initialization."""
            self._script_storage = "mock_storage"

        def set_execution_error(self, details):
            """Mock error setting."""
            self.execution_error_occurred = True
            self.execution_error_details = details

        def acknowledge_execution_error(self):
            """Mock error acknowledgment."""
            self.execution_error_acknowledged = True

        def clear_execution_error(self):
            """Mock error clearing."""
            self.execution_error_occurred = False
            self.execution_error_acknowledged = False
            self.execution_error_details = {}

    return ProgressState(stage_level)

def test_integration_scenarios(results):
    """Test 1: Integration Testing - Real-world upgrade scenarios."""
    print("\n" + "="*60)
    print("🔧 INTEGRATION TESTING - Real-world upgrade scenarios")
    print("="*60)

    # Test 1.1: Stage 1→4 upgrade without reversion
    try:
        mock_st = MockStreamlit()
        progress_state = create_progress_state(4)  # Progress through Stage 4

        # Simulate old state without current_stage
        mock_st.session_state["state"] = progress_state

        # Initialize new state manager
        new_state = StateManager()
        new_state.init_in_session(mock_st)

        # Get upgraded state
        upgraded_state = mock_st.session_state["state"]

        # Verify no Stage 1 reversion
        if hasattr(upgraded_state, 'current_stage'):
            expected_stage = StateStage.STAGE4_DETECT
            actual_stage = upgraded_state.current_stage

            if actual_stage == expected_stage:
                results.add_result("Integration: Stage 1→4 upgrade", True,
                                 f"Correctly upgraded to {actual_stage.get_display_name()}")
            else:
                results.add_result("Integration: Stage 1→4 upgrade", False,
                                 f"Expected {expected_stage}, got {actual_stage}", is_critical=True)
        else:
            results.add_result("Integration: Stage 1→4 upgrade", False,
                             "Upgraded state missing current_stage", is_critical=True)

    except Exception as e:
        results.add_result("Integration: Stage 1→4 upgrade", False,
                         f"Exception: {str(e)}", is_critical=True)

    # Test 1.2: Multiple upgrade cycles
    try:
        success_count = 0
        total_cycles = 5

        for i in range(total_cycles):
            mock_st = MockStreamlit()
            progress_state = create_progress_state(3)  # Stage 3 progress
            mock_st.session_state["state"] = progress_state

            new_state = StateManager()
            new_state.init_in_session(mock_st)

            upgraded_state = mock_st.session_state["state"]
            if (hasattr(upgraded_state, 'current_stage') and
                upgraded_state.current_stage != StateStage.STAGE1_UPLOAD):
                success_count += 1

        if success_count == total_cycles:
            results.add_result("Integration: Multiple upgrade cycles", True,
                             f"All {total_cycles} cycles successful")
        else:
            results.add_result("Integration: Multiple upgrade cycles", False,
                             f"Only {success_count}/{total_cycles} cycles successful", is_critical=True)

    except Exception as e:
        results.add_result("Integration: Multiple upgrade cycles", False,
                         f"Exception: {str(e)}", is_critical=True)

def test_stress_scenarios(results):
    """Test 2: Stress Testing - High-load conditions."""
    print("\n" + "="*60)
    print("⚡ STRESS TESTING - High-load conditions")
    print("="*60)

    # Test 2.1: Rapid stage transitions
    try:
        state = StateManager()
        state.uploaded_excel = "test.xlsx"
        state.test_cases = [{"Test Case ID": "TC001"}]
        state.website_url = "https://test.com"

        # Perform rapid transitions
        transition_count = 0
        failed_transitions = 0

        for i in range(20):  # 20 rapid transitions
            try:
                if i % 2 == 0:
                    result = state.advance_to(StateStage.STAGE2_WEBSITE, f"Rapid transition {i}")
                else:
                    result = state.advance_to(StateStage.STAGE3_CONVERT, f"Rapid transition {i}")

                if result:
                    transition_count += 1
                else:
                    failed_transitions += 1
            except Exception as e:
                failed_transitions += 1

        if failed_transitions == 0:
            results.add_result("Stress: Rapid stage transitions", True,
                             f"Completed {transition_count} transitions successfully")
        else:
            results.add_result("Stress: Rapid stage transitions", False,
                             f"{failed_transitions} transitions failed")

    except Exception as e:
        results.add_result("Stress: Rapid stage transitions", False,
                         f"Exception: {str(e)}")

    # Test 2.2: Rate limiting effectiveness
    try:
        state = StateManager()
        state.uploaded_excel = "test.xlsx"
        state.test_cases = [{"Test Case ID": "TC001"}]

        # Test rapid update_stage_based_on_completion calls
        first_call = state.update_stage_based_on_completion()
        immediate_call = state.update_stage_based_on_completion()  # Should be rate limited

        if immediate_call == False:  # Rate limited
            results.add_result("Stress: Rate limiting", True,
                             "Rate limiting correctly blocked rapid calls")
        else:
            results.add_result("Stress: Rate limiting", False,
                             "Rate limiting failed to block rapid calls")

    except Exception as e:
        results.add_result("Stress: Rate limiting", False,
                         f"Exception: {str(e)}")

def test_edge_cases(results):
    """Test 3: Edge Case Testing - Problematic scenarios."""
    print("\n" + "="*60)
    print("🔍 EDGE CASE TESTING - Problematic scenarios")
    print("="*60)

    # Test 3.1: File upload followed by immediate navigation
    try:
        state = StateManager()

        # Simulate file upload
        state.uploaded_excel = "test.xlsx"
        state.test_cases = [{"Test Case ID": "TC001"}]
        state.current_stage = StateStage.STAGE2_WEBSITE

        # Immediate navigation attempt
        result = state.advance_to(StateStage.STAGE1_UPLOAD, "Immediate navigation test")

        # Should be blocked by safety mechanisms
        if result == False and state.current_stage == StateStage.STAGE2_WEBSITE:
            results.add_result("Edge Case: File upload + navigation", True,
                             "Safety mechanism correctly blocked reversion")
        else:
            results.add_result("Edge Case: File upload + navigation", False,
                             f"Unexpected reversion allowed: result={result}, stage={state.current_stage}",
                             is_critical=True)

    except Exception as e:
        results.add_result("Edge Case: File upload + navigation", False,
                         f"Exception: {str(e)}", is_critical=True)

    # Test 3.2: Invalid stage transition attempts
    try:
        state = StateManager()
        state.current_stage = StateStage.STAGE5_DATA

        # Try invalid backward transition
        result = state.advance_to(StateStage.STAGE2_WEBSITE, "Invalid backward transition")

        if result == False and state.current_stage == StateStage.STAGE5_DATA:
            results.add_result("Edge Case: Invalid transitions", True,
                             "Invalid transition correctly blocked")
        else:
            results.add_result("Edge Case: Invalid transitions", False,
                             f"Invalid transition allowed: result={result}")

    except Exception as e:
        results.add_result("Edge Case: Invalid transitions", False,
                         f"Exception: {str(e)}")

    # Test 3.3: Session state corruption simulation
    try:
        mock_st = MockStreamlit()

        # Create corrupted state (missing critical fields but with required methods)
        class CorruptedState:
            def __init__(self):
                self.uploaded_excel = "test.xlsx"
                # Missing other critical fields but add required methods
                self._script_storage = None

            def _init_script_storage(self):
                """Mock script storage initialization."""
                self._script_storage = "mock_storage"

        mock_st.session_state["state"] = CorruptedState()

        # Try to initialize with corrupted state
        new_state = StateManager()
        new_state.init_in_session(mock_st)

        # Should handle gracefully
        upgraded_state = mock_st.session_state["state"]
        if hasattr(upgraded_state, 'current_stage'):
            results.add_result("Edge Case: Session corruption", True,
                             "Gracefully handled corrupted session state")
        else:
            results.add_result("Edge Case: Session corruption", False,
                             "Failed to handle corrupted session state")

    except Exception as e:
        results.add_result("Edge Case: Session corruption", False,
                         f"Exception: {str(e)}")

def test_end_to_end_workflow(results):
    """Test 4: End-to-End Workflow Testing."""
    print("\n" + "="*60)
    print("🔄 END-TO-END WORKFLOW TESTING")
    print("="*60)

    try:
        state = StateManager()
        monitor = get_stage_monitor()
        monitor.transitions.clear()  # Clear previous transitions

        # Simulate complete workflow Stage 1 → 8
        workflow_stages = [
            (StateStage.STAGE1_UPLOAD, "File upload"),
            (StateStage.STAGE2_WEBSITE, "Website configuration"),
            (StateStage.STAGE3_CONVERT, "Test case conversion"),
            (StateStage.STAGE4_DETECT, "UI element detection"),
            (StateStage.STAGE5_DATA, "Test data configuration"),
            (StateStage.STAGE6_GENERATE, "Script generation"),
            (StateStage.STAGE7_EXECUTE, "Script execution"),
            (StateStage.STAGE8_OPTIMIZE, "Script optimization")
        ]

        successful_transitions = 0

        for target_stage, description in workflow_stages:
            result = state.advance_to(target_stage, description)
            if result and state.current_stage == target_stage:
                successful_transitions += 1
            else:
                break

        # Check monitoring system
        recorded_transitions = len(monitor.transitions)

        if successful_transitions == len(workflow_stages):
            results.add_result("E2E: Complete workflow", True,
                             f"All {len(workflow_stages)} stages completed successfully")
        else:
            results.add_result("E2E: Complete workflow", False,
                             f"Only {successful_transitions}/{len(workflow_stages)} stages completed")

        if recorded_transitions > 0:
            results.add_result("E2E: Monitoring system", True,
                             f"Recorded {recorded_transitions} transitions")
        else:
            results.add_result("E2E: Monitoring system", False,
                             "No transitions recorded by monitoring system")

    except Exception as e:
        results.add_result("E2E: Complete workflow", False,
                         f"Exception: {str(e)}")

def test_regression_scenarios(results):
    """Test 5: Regression Testing - Existing functionality preservation."""
    print("\n" + "="*60)
    print("🔄 REGRESSION TESTING - Existing functionality")
    print("="*60)

    # Test 5.1: Manual navigation still works
    try:
        state = StateManager()
        state.uploaded_excel = "test.xlsx"
        state.test_cases = [{"Test Case ID": "TC001"}]
        state.website_url = "https://test.com"

        # Legal forward navigation
        result = state.advance_to(StateStage.STAGE3_CONVERT, "Manual navigation test")

        if result and state.current_stage == StateStage.STAGE3_CONVERT:
            results.add_result("Regression: Manual navigation", True,
                             "Legal navigation works correctly")
        else:
            results.add_result("Regression: Manual navigation", False,
                             f"Legal navigation failed: result={result}, stage={state.current_stage}")

    except Exception as e:
        results.add_result("Regression: Manual navigation", False,
                         f"Exception: {str(e)}")

    # Test 5.2: Error handling preservation
    try:
        state = StateManager()

        # Test error state management
        error_details = {"error": "test error"}
        state.set_execution_error(error_details)

        if (state.execution_error_occurred and
            state.execution_error_details == error_details):
            results.add_result("Regression: Error handling", True,
                             "Error handling functionality preserved")
        else:
            results.add_result("Regression: Error handling", False,
                             "Error handling functionality broken")

    except Exception as e:
        results.add_result("Regression: Error handling", False,
                         f"Exception: {str(e)}")

    # Test 5.3: Stage determination accuracy
    try:
        state = StateManager()

        # Test various progress levels with detailed debugging
        # NOTE: Level 1 progress (file uploaded) means Stage 1 is COMPLETE, so we advance to Stage 2
        test_cases = [
            (0, StateStage.STAGE1_UPLOAD),  # No progress = Stage 1
            (1, StateStage.STAGE2_WEBSITE), # File uploaded = Stage 1 complete, advance to Stage 2
            (2, StateStage.STAGE3_CONVERT), # Website configured = Stage 2 complete, advance to Stage 3
            (3, StateStage.STAGE4_DETECT),  # Test case converted = Stage 3 complete, advance to Stage 4
            (4, StateStage.STAGE4_DETECT)   # Stage 4 progress but not complete = stay at Stage 4
        ]

        all_correct = True
        failed_case = None

        for level, expected_stage in test_cases:
            progress_state = create_progress_state(level)
            determined_stage = state._determine_stage_from_state(progress_state)

            print(f"    Level {level}: Expected {expected_stage.get_display_name()}, Got {determined_stage.get_display_name()}")

            if determined_stage != expected_stage:
                all_correct = False
                failed_case = f"Level {level}: Expected {expected_stage.get_display_name()}, Got {determined_stage.get_display_name()}"
                break

        if all_correct:
            results.add_result("Regression: Stage determination", True,
                             "Stage determination accuracy preserved")
        else:
            results.add_result("Regression: Stage determination", False,
                             f"Stage determination accuracy compromised - {failed_case}")

    except Exception as e:
        results.add_result("Regression: Stage determination", False,
                         f"Exception: {str(e)}")

def run_comprehensive_tests():
    """Run all comprehensive tests and return results."""
    print("🧪 STARTING COMPREHENSIVE STATE MANAGEMENT TESTING")
    print("="*80)
    print("Testing GretahAI ScriptWeaver state management fixes...")
    print("This will validate that Stage 1 reversion issues are completely resolved.")
    print("="*80)

    results = TestResults()

    # Run all test categories
    test_integration_scenarios(results)
    test_stress_scenarios(results)
    test_edge_cases(results)
    test_end_to_end_workflow(results)
    test_regression_scenarios(results)

    # Print comprehensive summary
    results.print_summary()

    return results.failed_tests == 0

if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
