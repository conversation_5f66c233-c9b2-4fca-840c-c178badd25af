{"test_case_id": "TC_021", "timestamp": "2025-05-30T13:09:14.980128", "data_hash": "98a224236999558851580616b3ab678921d8e0a1053fb002a971b9b7e7014dbd", "step_count": 3, "metadata": {"source": "initial_conversion", "conversion_method": "ai_generated", "validation_score": 5, "step_analysis": {"requires_ui_elements": true, "reason": "UI interaction 'click' detected in test steps", "actions": ["type", "type", "click"], "locator_strategies": ["css", "css", "css"]}, "conversion_timestamp": "2025-05-30T13:09:14.976732", "test_case_objective": "Verify that the system rejects email addresses with excessive special characters.", "hybrid_editing_enabled": true, "has_manual_steps": true, "save_timestamp": "2025-05-30T13:09:14.980128"}, "step_data": [{"step_no": "1", "step_type": "ui", "action": "type", "locator_strategy": "css", "locator": "#email", "test_data_param": "{{special_chars_only}}", "expected_result": "error_message", "assertion_type": "element_visible", "condition": "", "timeout": 10, "step_description": "Enter an email address containing only special characters into the email field."}, {"step_no": "2", "step_type": "ui", "action": "type", "locator_strategy": "css", "locator": "#email", "test_data_param": "{{special_chars_interpersed}}", "expected_result": "error_message", "assertion_type": "element_visible", "condition": "", "timeout": 10, "step_description": "Enter an email address with special characters interspersed into the email field."}, {"step_no": "3", "step_type": "ui", "action": "click", "locator_strategy": "css", "locator": "#next_button", "test_data_param": "", "expected_result": "inactive_button_or_error", "assertion_type": "element_not_interactable_or_element_visible", "condition": "", "timeout": 10, "step_description": "Click the 'Next' button after entering an email with excessive special characters."}]}