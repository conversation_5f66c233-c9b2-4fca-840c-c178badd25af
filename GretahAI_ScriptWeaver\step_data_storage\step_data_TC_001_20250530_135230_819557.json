{"test_case_id": "TC_001", "timestamp": "2025-05-30T13:52:30.819557", "data_hash": "c19d27bb35f72a58568bbbb275d98f4c5979f0e79c454eea48222fe6ccf532c1", "step_count": 4, "metadata": {"source": "real_time_update", "operation": "script_generated_step_4", "update_timestamp": "2025-05-30T13:52:30.819557", "step_count": 4, "step_no": "4", "script_file_path": "generated_tests\\test_TC_001_4_1748638350_merged.py", "script_content_length": 3715, "step_specific_file": "generated_tests\\test_TC_001_4_1748638350_step_only.py", "generation_method": "ai_generated", "test_case_objective": "Verify that a user with valid credentials can successfully log in.", "hybrid_editing_enabled": false, "has_manual_steps": false, "save_timestamp": "2025-05-30T13:52:30.819557"}, "step_data": [{"step_no": "1", "step_type": "ui", "action": "navigate", "locator_strategy": "", "locator": "", "test_data_param": "{{login_page_url}}", "expected_result": "login_page", "assertion_type": "url_equals", "condition": "", "timeout": 10, "step_description": "Navigate to the login page", "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_1_1748638159_merged.py", "_script_content_length": 1249, "_script_generation_timestamp": "2025-05-30T13:49:19.378347", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_1_1748638159_step_only.py"}, {"step_no": "2", "step_type": "ui", "action": "type", "locator_strategy": "id", "locator": "username", "test_data_param": "{{username}}", "expected_result": "username_entered", "assertion_type": "text_equals", "condition": "", "timeout": 10, "step_description": "Enter a valid user ID in the designated field", "_test_data": {"manual_input_for_step_2": "tomsmith"}, "_test_data_generated": true, "_test_data_timestamp": "2025-05-30T13:50:08.786409", "_test_data_method": "manual_entry", "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_2_1748638219_merged.py", "_script_content_length": 1969, "_script_generation_timestamp": "2025-05-30T13:50:19.187563", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_2_1748638219_step_only.py"}, {"step_no": "3", "step_type": "ui", "action": "type", "locator_strategy": "id", "locator": "password", "test_data_param": "{{password}}", "expected_result": "password_entered", "assertion_type": "text_equals", "condition": "", "timeout": 10, "step_description": "Enter a valid password in the designated field", "_test_data": {"manual_input_for_step_3": "SuperSecretPassword!"}, "_test_data_generated": true, "_test_data_timestamp": "2025-05-30T13:51:22.216041", "_test_data_method": "manual_entry", "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_3_1748638291_merged.py", "_script_content_length": 2840, "_script_generation_timestamp": "2025-05-30T13:51:31.666037", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_3_1748638291_step_only.py"}, {"step_no": "4", "step_type": "ui", "action": "click", "locator_strategy": "css", "locator": "#login-button", "test_data_param": "", "expected_result": "dashboard_page", "assertion_type": "element_visible", "condition": "", "timeout": 10, "step_description": "Click the login button", "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_4_1748638350_merged.py", "_script_content_length": 3715, "_script_generation_timestamp": "2025-05-30T13:52:30.819557", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_4_1748638350_step_only.py"}]}